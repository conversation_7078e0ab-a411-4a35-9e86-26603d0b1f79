from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone

from .models import TodoList
from .enum import TodoListStatusEnum, TodoListTypeEnum


@admin.register(TodoList)
class TodoListAdmin(admin.ModelAdmin):
    # 列表页显示字段
    list_display = [
        'rid',
        'todo_type_display',
        'todo_status_display',
        'maternity_center',
        'assign_display',
        'assigned_to_display',
        'maternity_info',
        'todo_content_short',
        'complete_time',
        'created_at'
    ]

    # 列表页过滤器
    list_filter = [
        'todo_status',
        'todo_type',
        'maternity_center',
        'created_at',
        'complete_time',
        ('assign', admin.RelatedOnlyFieldListFilter),
        ('assigned_to', admin.RelatedOnlyFieldListFilter),
    ]

    # 搜索字段
    search_fields = [
        'rid',
        'todo_content',
        'todo_remark',
        'complete_feedback',
        'assign__name',
        'assigned_to__name',
        'maternity_admission__maternity__name',
        'maternity_admission__room__room_number',
    ]

    # 详情页字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('rid', 'maternity_center', 'todo_type', 'todo_status')
        }),
        ('人员分配', {
            'fields': ('assign', 'assigned_to')
        }),
        ('关联信息', {
            'fields': ('maternity_admission',)
        }),
        ('待办内容', {
            'fields': ('todo_content', 'todo_remark')
        }),
        ('完成信息', {
            'fields': ('complete_feedback', 'complete_time'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    # 只读字段
    readonly_fields = ['rid', 'created_at', 'updated_at']

    # 每页显示数量
    list_per_page = 25

    # 默认排序
    ordering = ['-created_at']

    # 自定义显示方法
    def todo_type_display(self, obj):
        """待办事项类型显示"""
        color_map = {
            TodoListTypeEnum.FEEDBACK: '#17a2b8',      # 客户反馈 - 蓝色
            TodoListTypeEnum.COMPLAINT: '#dc3545',     # 客户投诉 - 红色
            TodoListTypeEnum.DAILY_CLEANING: '#28a745', # 日常清洁 - 绿色
            TodoListTypeEnum.CUSTOMER_CARE: '#ffc107',  # 客户关怀 - 黄色
            TodoListTypeEnum.CUSTOMER_VISIT: '#6f42c1', # 客户回访 - 紫色
            TodoListTypeEnum.OTHER: '#6c757d',         # 其他 - 灰色
        }
        color = color_map.get(obj.todo_type, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_todo_type_display()
        )
    todo_type_display.short_description = '待办类型'

    def todo_status_display(self, obj):
        """待办状态显示"""
        color_map = {
            TodoListStatusEnum.PENDING: '#ffc107',      # 待办 - 黄色
            TodoListStatusEnum.IN_PROGRESS: '#17a2b8',  # 进行中 - 蓝色
            TodoListStatusEnum.COMPLETED: '#28a745',    # 已完成 - 绿色
        }
        color = color_map.get(obj.todo_status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px;">{}</span>',
            color,
            obj.get_todo_status_display()
        )
    todo_status_display.short_description = '状态'

    def assign_display(self, obj):
        """指派人显示"""
        if obj.assign:
            return format_html(
                '<a href="{}" target="_blank">{}</a>',
                reverse('admin:user_staff_change', args=[obj.assign.pk]),
                obj.assign.name
            )
        return '-'
    assign_display.short_description = '指派人'

    def assigned_to_display(self, obj):
        """被指派人显示"""
        if obj.assigned_to:
            return format_html(
                '<a href="{}" target="_blank">{}</a>',
                reverse('admin:user_staff_change', args=[obj.assigned_to.pk]),
                obj.assigned_to.name
            )
        return '-'
    assigned_to_display.short_description = '被指派人'

    def maternity_info(self, obj):
        """产妇信息显示"""
        if obj.maternity_admission and obj.maternity_admission.maternity:
            maternity = obj.maternity_admission.maternity
            room = obj.maternity_admission.room
            room_info = f" - {room.room_number}" if room else ""
            return format_html(
                '<a href="{}" target="_blank">{}{}</a>',
                reverse('admin:customer_service_core_records_maternity_admission_change', args=[obj.maternity_admission.pk]),
                maternity.name,
                room_info
            )
        return '-'
    maternity_info.short_description = '产妇信息'

    def todo_content_short(self, obj):
        """待办内容简短显示"""
        if len(obj.todo_content) > 50:
            return obj.todo_content[:50] + '...'
        return obj.todo_content
    todo_content_short.short_description = '待办内容'

    # 自定义查询集优化
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related(
            'maternity_center',
            'assign',
            'assigned_to',
            'maternity_admission__maternity',
            'maternity_admission__room'
        )

    # 自定义操作
    actions = ['mark_as_completed', 'mark_as_in_progress', 'mark_as_pending']

    def mark_as_completed(self, request, queryset):
        """批量标记为已完成"""
        updated = queryset.update(
            todo_status=TodoListStatusEnum.COMPLETED,
            complete_time=timezone.now()
        )
        self.message_user(request, f'成功将 {updated} 个待办事项标记为已完成。')
    mark_as_completed.short_description = '标记为已完成'

    def mark_as_in_progress(self, request, queryset):
        """批量标记为进行中"""
        updated = queryset.update(todo_status=TodoListStatusEnum.IN_PROGRESS)
        self.message_user(request, f'成功将 {updated} 个待办事项标记为进行中。')
    mark_as_in_progress.short_description = '标记为进行中'

    def mark_as_pending(self, request, queryset):
        """批量标记为待办"""
        updated = queryset.update(todo_status=TodoListStatusEnum.PENDING)
        self.message_user(request, f'成功将 {updated} 个待办事项标记为待办。')
    mark_as_pending.short_description = '标记为待办'

    # 自定义表单验证
    def save_model(self, request, obj, form, change):
        """保存模型时的自定义逻辑"""
        # 如果状态改为已完成且没有完成时间，自动设置完成时间
        if obj.todo_status == TodoListStatusEnum.COMPLETED and not obj.complete_time:
            obj.complete_time = timezone.now()
        # 如果状态不是已完成，清空完成时间
        elif obj.todo_status != TodoListStatusEnum.COMPLETED:
            obj.complete_time = None
        super().save_model(request, obj, form, change)

    # 自定义列表页面的额外上下文
    def changelist_view(self, request, extra_context=None):
        """自定义列表页面，添加统计信息"""
        extra_context = extra_context or {}

        # 应用当前的过滤器
        cl = self.get_changelist_instance(request)
        filtered_queryset = cl.get_queryset(request)

        # 统计各状态的数量
        stats = {
            'total': filtered_queryset.count(),
            'pending': filtered_queryset.filter(todo_status=TodoListStatusEnum.PENDING).count(),
            'in_progress': filtered_queryset.filter(todo_status=TodoListStatusEnum.IN_PROGRESS).count(),
            'completed': filtered_queryset.filter(todo_status=TodoListStatusEnum.COMPLETED).count(),
        }

        extra_context['todo_stats'] = stats
        return super().changelist_view(request, extra_context)

    # 自定义权限控制
    def has_change_permission(self, request, obj=None):
        """自定义修改权限"""
        # 基础权限检查
        if not super().has_change_permission(request, obj):
            return False

        # 如果是超级用户，允许所有操作
        if request.user.is_superuser:
            return True

        # 这里可以添加更多自定义权限逻辑
        # 例如：只允许修改自己创建的或分配给自己的待办事项
        return True

    # 自定义删除权限
    def has_delete_permission(self, request, obj=None):
        """自定义删除权限"""
        # 基础权限检查
        if not super().has_delete_permission(request, obj):
            return False

        # 如果是超级用户，允许删除
        if request.user.is_superuser:
            return True

        # 不允许删除已完成的待办事项
        if obj and obj.todo_status == TodoListStatusEnum.COMPLETED:
            return False

        return True
